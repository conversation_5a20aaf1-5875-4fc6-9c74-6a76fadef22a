var dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
const ToastDialog = require("../../utils/toastDialog.js");
var diyecharts = require("../../utils/diyecharts.js");
const rolePermission = require("../../utils/rolePermission.js");
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    level:0,
    isnew: false, //判断是否新用户（未登录）
    timeshow: false,
    yearshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    currentYearDate: new Date().getTime(),
    startTime: dateUtil.getLast30DaysStartDate(),
    endTime: dateUtil.getLast30DaysEndDate(),
    selectedYear: new Date().getFullYear(),
    active: 0, // 默认选中"年度"选项卡
    sRactive1: 0, // 默认选中"全部"选项卡
    sRactive2: 0,
    sRactive3: 0,
    tType: 1,
    timeType: 1,
    title1: [
      "工单汇总",
      "工单汇总",
      "完工率汇总",
      "录单汇总",
      "催单汇总", ,
      "一次修复汇总", ,
      "安装整改完成汇总",
    ],
    title2: [
      "接单及时率",
      "派工及时性",
      "完工率",
      "录单率",
      "催单率", ,
      "一次修复率", ,
      "安装整改完成率",
    ],
    title3: ["接单超时率", "", "完工超时率", "", "", "", ""],
    title4: ["", "", "完工超时率(已接单)", "", "", "", ""],
    option: [{
        text: "接单及时性分析",
        value: 0,
      },
      {
        text: "派工及时性分析",
        value: 1,
      },
      {
        text: "完工及时性分析",
        value: 2,
      },
      {
        text: "录单率分析",
        value: 3,
      },
      {
        text: "催单率分析",
        value: 4,
      },
      {
        text: "一次修复率分析",
        value: 5,
      },
      {
        text: "安装整改完成率分析",
        value: 6,
      },
    ],
    value: 0,
    yearColumns: [{
      values: [
        "2020",
        "2021",
        "2022",
        "2023",
        "2024",
        "2025",
        "2026",
        "2027",
        "2028",
        "2029",
        "2030",
      ],
    }, ],
    yearIndex: 0, // 初始值，将在onLoad中根据selectedYear计算
    yearshow: false,
    regioninfo: [],
    selectedRegion: "暂无",
    selectedRegionValue: "",
    regionIndex: 0,
    regionshow: false,
    summaryinfo: [],
    serviceReportinfo1: [],
    serviceReportinfo2: [],
    serviceReportinfo3: [],
    pieec: {
      onInit: null,
    },
    lineec: {
      onInit: null,
    },
    summaryinfoloading: true,
    serviceReportinfo1loading: true,
    serviceReportinfowloading: true
  },

  RegionGetList() {
    request
      .get({
        url: "Region/GetList",
        load: false,
      })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          // 获取区域数据
          const regions = res.data;

          // 创建区域选择列表
          const regionLabels = regions.map((item) => item.label);
          const regionColumns = [{
            values: regionLabels
          }];

          // 默认选择第一个区域
          const firstRegion = regions[0].label;

          this.setData({
            regioninfo: regions,
            regionColumns: regionColumns,
            selectedRegion: firstRegion,
            selectedRegionValue: regions[0].value,
            regionIndex: 0,
          });

          this.init1();
        }
      });
  },

  //接单及时性
  ServiceFormMasterGetSummary() {
    this.setData({
      summaryinfoloading: true
    })
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormAccept/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfoloading: false,
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      });
  },
  //及时率
  ServiceFormMasterGetServiceReport1() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.level);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormAccept/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });

        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch((err) => {
        console.error("获取及时率数据失败:", err);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },
  //超时率
  ServiceFormMasterGetServiceReport2() {
    this.setData({
      serviceReportinfowloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.level);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 2,
      timeType: this.data.sRactive1,
    };
    request
      .post({
        url: "ServiceFormAccept/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });
        this.setData({
          serviceReportinfo2: processedData,
          serviceReportinfowloading: false
        });
      })
      .catch((err) => {
        console.error("获取超时率数据失败:", err);
        this.setData({
          serviceReportinfo2: [],
          serviceReportinfowloading: false
        });
      });
  },
  ServiceFormMasterGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    }
    request
      .post({
        url: "ServiceFormAccept/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(
          this,
          linedata,
          xAxisData,
          timeType,
          "接单及时率"
        );
      });
  },
  //派工
  ServiceFormDispatchGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormDispatch/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      });
  },
  ServiceFormDispatchGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.level);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormDispatch/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });
        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch((err) => {
        console.error("获取派工及时性数据失败:", err);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },
  ServiceFormDispatchGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormDispatch/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "派工及时性");
      });
  },


  //完工

  ServiceFormFinishedGetSummary() {
     const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormFinished/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
      // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      });
  },
  ServiceFormFinishedGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      timeType: this.data.sRactive1,
    };
    request
      .post({
        url: "ServiceFormFinished/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });

        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch((err) => {
        console.error("获取完工率数据失败:", err);
        this.setData({
          serviceReportinfo1loading: false
        });
      });
  },
  ServiceFormFinishedGetServiceOutReport1() {
    this.setData({
      serviceReportinfowloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      timeType: this.data.sRactive2,
    };
    request
      .post({
        url: "ServiceFormFinished/GetServiceOutReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });

        this.setData({
          serviceReportinfo2: processedData,
          serviceReportinfowloading: false
        });
      })
      .catch((err) => {
        console.error("获取完工超时率数据失败:", err);
        this.setData({
          serviceReportinfowloading: false
        });
      });
  },

  ServiceFormFinishedGetServiceOutReport2() {
    this.setData({
      serviceReportinfowloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
      timeType: this.data.sRactive3,
    };
    request
      .post({
        url: "ServiceFormFinished/GetServiceOutReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });
        this.setData({
          serviceReportinfo3: processedData,
          serviceReportinfowloading: false
        });
      })
      .catch((err) => {
        console.error("获取完工超时率(已接单)数据失败:", err);
        this.setData({
          serviceReportinfowloading: false
        });
      });
  },
  ServiceFormFinishedGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormFinished/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "派工及时性");
      });
  },

  // 录单率汇总
  ServiceFormRecordGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormRecord/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch(error => {
        console.error("获取录单率汇总数据失败:", error);
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 录单率
  ServiceFormRecordGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormRecord/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });
        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch(error => {
        console.error("获取录单率数据失败:", error);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },

  // 录单率统计
  ServiceFormRecordGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level:this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormRecord/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "录单率");
      })
      .catch(error => {
        console.error("获取录单率统计数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 催单率汇总
  ServiceFormRemindGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormRemind/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch(error => {
        console.error("获取催单率汇总数据失败:", error);
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 催单率
  ServiceFormRemindGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormRemind/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });
        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch(error => {
        console.error("获取催单率数据失败:", error);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },

  // 催单率统计
  ServiceFormRemindGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level:this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormRemind/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "催单率");
      })
      .catch(error => {
        console.error("获取催单率统计数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 一次修复率汇总
  ServiceFormRepairGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormRepair/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch(error => {
        console.error("获取一次修复率汇总数据失败:", error);
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 一次修复率
  ServiceFormRepairGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormRepair/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });
        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch(error => {
        console.error("获取一次修复率数据失败:", error);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },

  // 一次修复率统计
  ServiceFormRepairGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormRepair/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "一次修复率");
      })
      .catch(error => {
        console.error("获取一次修复率统计数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 安装整改完成率汇总
  ServiceFormInstRectGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "ServiceFormInstRect/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfo: res.data,
        });
        await ensurePieReady(); // 等待图表实例创建
        // 处理数据（根据实际接口结构调整）
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch(error => {
        console.error("获取安装整改完成率汇总数据失败:", error);
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },

  // 安装整改完成率
  ServiceFormInstRectGetServiceReport() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ServiceFormInstRect/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });
        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch(error => {
        console.error("获取安装整改完成率数据失败:", error);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },

  // 安装整改完成率统计
  ServiceFormInstRectGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    if (timeType == 1) {
      data = {
        level:this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level:this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "ServiceFormInstRect/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, "安装整改完成率");
      })
      .catch(error => {
        console.error("获取安装整改完成率统计数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          }, {
            replaceMerge: ["graphic"],
          });
        }
      });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 计算当前年份对应的yearIndex
    const currentYear = new Date().getFullYear().toString();
    const yearValues = this.data.yearColumns[0].values;
    const yearIndex = yearValues.indexOf(currentYear);

    // 如果找到当前年份在数组中的位置，则更新yearIndex
    if (yearIndex !== -1) {
      this.setData({
        yearIndex: yearIndex,
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先设置图表初始化函数，但不会立即执行
    this.setData({
        pieec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstancePie = diyecharts.initPieChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstancePie;
          },
        },
        lineec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstanceLine = diyecharts.initLineChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstanceLine;
          },
        },
        userInfo: wx.getStorageSync("userInfo"),
        isnew: app.globalData.isnew,
        level: app.globalData.level,
        shouldHideTabs: rolePermission.shouldHideTabs(wx.getStorageSync("userInfo"))
      },
      () => {
        if(!this.data.isnew){
          // 只有在已登录状态下才加载数据
          this.RegionGetList();
          this.init();
        }
      }
    );
  },

  // 登录按钮点击事件
  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },
  onUnload() {
    diyecharts.onUnload(this);
  },
  init() {
    switch (this.data.value) {
      case 0:
        this.ServiceFormMasterGetSummary();
        this.ServiceFormMasterGetServiceReport1();
        this.ServiceFormMasterGetServiceReport2();
        break;
      case 1:
        this.ServiceFormDispatchGetSummary();
        this.ServiceFormDispatchGetServiceReport();
        break;
      case 2:
        this.ServiceFormFinishedGetSummary();
        this.ServiceFormFinishedGetServiceReport();
        this.ServiceFormFinishedGetServiceOutReport1();
        this.ServiceFormFinishedGetServiceOutReport2();
        break;
      case 3:
        this.ServiceFormRecordGetSummary();
        this.ServiceFormRecordGetServiceReport();
        break;
      case 4:
        this.ServiceFormRemindGetSummary();
        this.ServiceFormRemindGetServiceReport();
        break;
      case 5:
        this.ServiceFormRepairGetSummary();
        this.ServiceFormRepairGetServiceReport();
        break;
      case 6:
        this.ServiceFormInstRectGetSummary();
        this.ServiceFormInstRectGetServiceReport();
        break;
    }
  },
  init1() {
    switch (this.data.value) {
      case 0:
        this.ServiceFormMasterGetStat();
        break;
      case 1:
        this.ServiceFormDispatchGetStat();
        break;
      case 2:
        this.ServiceFormFinishedGetStat();
        break;
      case 3:
        this.ServiceFormRecordGetStat();
        break;
      case 4:
        this.ServiceFormRemindGetStat();
        break;
      case 5:
        this.ServiceFormRepairGetStat();
        break;
      case 6:
        this.ServiceFormInstRectGetStat();
        break;
    }
  },

  //顶部选择日期范围的事件
  onOptionChange(e) {
    this.setData({
      value: e.detail,
    });
    this.init();
    this.init1();
  },


  startTimePopup() {
    var time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },
  endTimePopup() {
    var time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },
  onConfirm(e) {
    console.log(e.detail);
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });
    if (this.data.tType == 1) {
      this.setData({
        startTime: dateUtil.getDateObject(e.detail),
      });
    } else {
      this.setData({
        endTime: dateUtil.getDateObject(e.detail),
      });
    }
    this.init();
  },
  onClose() {
    this.setData({
      timeshow: false,
    });
  },

  // 统计 事件

  // 显示年份选择弹出层
  showYearPopup() {
    this.setData({
      yearshow: true,
    });
  },
  // 确认选择年份
  onYearConfirm(e) {
    const {
      value,
      index
    } = e.detail;
    const year = parseInt(value[0]);
    this.setData({
      selectedYear: year,
      yearIndex: index[0],
      yearshow: false,
    });
    this.init1();
  },

  ontjChange(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      active: index,
      timeType: index + 1, // 设置 timeType: 1-年度，2-自然月，3-自然周
    });

    this.init1();
  },

  // 取消选择年份
  onYearCancel() {
    this.setData({
      yearshow: false,
    });
  },

  // 显示区域选择弹出层
  showRegionPopup() {
    this.setData({
      regionshow: true,
    });
  },

  // 确认选择区域
  onRegionConfirm(e) {
    const {
      value,
      index
    } = e.detail;
    const regionLabel = value[0];

    // 找到对应的区域对象
    const selectedRegion = this.data.regioninfo.find(
      (item) => item.label === regionLabel
    );

    if (selectedRegion) {
      this.setData({
        selectedRegion: selectedRegion.label,
        selectedRegionValue: selectedRegion.value,
        regionIndex: index[0],
        regionshow: false,
      });
      // 选择年份后重新获取统计数据
      this.init1();
    }
  },

  // 取消选择区域
  onRegionCancel() {
    this.setData({
      regionshow: false,
    });
  },

  // 关闭区域选择弹出层
  onRegionClose() {
    this.setData({
      regionshow: false,
    });
  },

  // 权限级别tab切换事件
  onsrChange(e) {
    this.setData({
      sractive: e.detail.index
    });
    // 根据当前选择的分析类型调用对应的GetServiceReport API
    switch (this.data.value) {
      case 0:
        // 接单及时性分析
        this.ServiceFormMasterGetServiceReport1();
        this.ServiceFormMasterGetServiceReport2();
        break;
      case 1:
        // 派工及时性分析
        this.ServiceFormDispatchGetServiceReport();
        break;
      case 2:
        // 完工及时性分析
        this.ServiceFormFinishedGetServiceReport();
        this.ServiceFormFinishedGetServiceOutReport1();
        this.ServiceFormFinishedGetServiceOutReport2();
        break;
      case 3:
        // 录单率分析
        this.ServiceFormRecordGetServiceReport();
        break;
      case 4:
        // 催单率分析
        this.ServiceFormRemindGetServiceReport();
        break;
      case 5:
        // 一次修复率分析
        this.ServiceFormRepairGetServiceReport();
        break;
      case 6:
        // 安装整改完成率分析
        this.ServiceFormInstRectGetServiceReport();
        break;
    }
  },

  onSRChange1(e) {
    this.setData({
      sRactive1: e.detail.index,
    });
    if (this.data.value == 0) {
      this.ServiceFormMasterGetServiceReport2()
    } else {
      this.ServiceFormFinishedGetServiceReport()
    }
  },
  onSRChange2(e) {
    this.setData({
      sRactive2: e.detail.index,
    });

    this.ServiceFormFinishedGetServiceOutReport1()

  },
  onSRChange3(e) {
    this.setData({
      sRactive3: e.detail.index,
    });
    this.ServiceFormFinishedGetServiceOutReport2()

  }
});