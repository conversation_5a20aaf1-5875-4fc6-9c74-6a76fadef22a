# 角色权限控制工具使用说明

## 概述
`rolePermission.js` 是一个全局的角色权限控制工具，用于统一管理不同角色的权限和level参数。

## 功能特性
- 统一的角色权限管理
- 自动的level参数映射
- Tab显示控制
- 权限验证
- 可扩展的角色定义

## 使用方法

### 1. 引入工具
```javascript
const rolePermission = require("../../utils/rolePermission.js");
```

### 2. 在页面中使用

#### JavaScript文件 (index.js)
```javascript
Page({
  data: {
    userInfo: null,
    shouldHideTabs: false,
    sractive: 0
  },

  onShow() {
    const userInfo = wx.getStorageSync("userInfo");
    this.setData({
      userInfo: userInfo,
      shouldHideTabs: rolePermission.shouldHideTabs(userInfo)
    });
  },

  // API请求中使用权限控制
  getData() {
    // 自动根据角色获取正确的level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);
    // 自动根据角色获取正确的value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    const data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      value: value
    };

    request.post({
      url: "YourAPI/GetData",
      data
    });
  }
});
```

#### WXML文件 (index.wxml)
```xml
<!-- 根据权限控制tab显示 -->
<van-tabs wx:if="{{!shouldHideTabs}}" active="{{ sractive }}" bind:change="onTabChange" color="#1677ff">
  <van-tab title="区域"></van-tab>
  <van-tab title="城市"></van-tab>
  <van-tab title="门店"></van-tab>
  <van-tab title="工程师"></van-tab>
  <van-tab title="调度员"></van-tab>
</van-tabs>
```

## API参考

### 常量定义
```javascript
ROLE_IDS = {
  STORE_MANAGER: 6,    // 门店管理员
  ENGINEER: 7          // 工程师
}

LEVEL_MAPPING = {
  REGION: 1,      // 区域
  CITY: 2,        // 城市
  STORE: 3,       // 门店
  ENGINEER: 4,    // 工程师
  DISPATCHER: 5   // 调度员
}
```

### 主要方法

#### getLevelByRole(userInfo, defaultLevel)
根据用户角色获取对应的level参数
- `userInfo`: 用户信息对象
- `defaultLevel`: 默认level值（通常是选中的tab索引+1）
- 返回: 对应的level参数

#### shouldHideTabs(userInfo)
检查用户是否需要隐藏tab切换
- `userInfo`: 用户信息对象
- 返回: true表示需要隐藏tab，false表示显示tab

#### getRoleDisplayName(userInfo)
获取角色对应的显示名称
- `userInfo`: 用户信息对象
- 返回: 角色显示名称

#### getLevelDisplayName(level)
获取level对应的显示名称
- `level`: level参数
- 返回: level显示名称

#### hasLevelPermission(userInfo, targetLevel)
检查用户是否有访问特定level的权限
- `userInfo`: 用户信息对象
- `targetLevel`: 目标level
- 返回: true表示有权限，false表示无权限

#### getAccessibleLevels(userInfo)
获取用户可访问的level列表
- `userInfo`: 用户信息对象
- 返回: 可访问的level数组

#### getValueByRole(userInfo, defaultValue)
根据用户角色获取对应的value参数
- `userInfo`: 用户信息对象
- `defaultValue`: 默认value值（通常是选中的区域值）
- 返回: 对应的value参数
- 注意: 当角色6的servicePointId或角色7的engineerId为null时，自动返回0

## 角色权限规则

### 角色6 (门店管理员)
- 不显示tab切换和区域选择框
- level固定为3（门店）
- value固定为userInfo.servicePointId（为null时使用0）
- 只能访问门店及以下级别数据

### 角色7 (工程师)
- 不显示tab切换和区域选择框
- level固定为4（工程师）
- value固定为userInfo.engineerId（为null时使用0）
- 只能访问工程师及以下级别数据

### 其他角色
- 显示完整的tab切换和区域选择框
- level根据选中的tab动态变化
- value根据选中的区域动态变化
- 可访问所有级别数据

## 扩展说明

如需添加新的角色或修改权限规则，只需修改 `rolePermission.js` 文件中的常量定义和相关方法即可，所有使用该工具的页面会自动应用新的权限规则。

## 已应用页面
- 满意度分析页面 (`pages/satisfactionAnalysis`)
- 投诉分析页面 (`pages/complaintAnalysis`)

## 待应用页面
- 用户分析页面
- 工单分析页面
- 其他需要权限控制的页面
