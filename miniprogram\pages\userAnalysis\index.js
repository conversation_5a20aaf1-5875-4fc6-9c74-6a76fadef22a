// pages/userAnalysis/index.js
var dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
var diyecharts = require("../../utils/diyecharts.js");
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    level:0,
    isnew: false, //判断是否新用户（未登录）
    timeshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    startTime: dateUtil.getStartYearDate(),
    endTime: dateUtil.getEndYearDate(),
    selectedYear: new Date().getFullYear(),
    active: 0, // 默认选中"年度"选项卡
    tType: 1,
    timeType: 1,
    title1: ["录入汇总", "活跃用户汇总"],
    title2: ["渠道录入率", ""],
    title3: ["录入率", ""],
    option: [
      {
        text: "用户录入率分析",
        value: 0,
      },
      {
        text: "用户活跃度分析",
        value: 1,
      },
    ],
    value: 0,
    vractive:0,
    yearColumns: [
      {
        values: [
          "2020",
          "2021",
          "2022",
          "2023",
          "2024",
          "2025",
          "2026",
          "2027",
          "2028",
          "2029",
          "2030",
        ],
      },
    ],
    yearIndex: 0, // 初始值，将在onLoad中根据selectedYear计算
    yearshow: false,
    regioninfo: [],
    selectedRegion: "暂无",
    selectedRegionValue: "",
    regionIndex: 0,
    regionshow: false,
    pieec: {
      onInit: null,
    },
    lineec: {
      onInit: null,
    },

    summaryinfo: [],
    serviceReportinfo: [],
    channelinfo:[],
    summaryinfoloading:true,
    channelinfoloading:true,
    serviceReportinfoloading:true
  },
  //用户录入率分析
  UserProductGetSummary() {
    this.setData({
      summaryinfoloading:true
    })
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "UserProduct/GetSummary",
        data,
        load: false,
      })
      .then((res) => {
        this.setData({
          summaryinfo: res.data,
          summaryinfoloading:false
        });
      });
  },
  UserProductGetProd() {
    const ensurePieReady = diyecharts.createChartGuard(this,"chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    diyecharts.showLoading(this,"chartInstancePie")
    request
      .post({
        url: "UserProduct/GetProd",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        const piedata = res.data.map((e) => ({
          value: e.enterQauntity,
          name: e.title,
        }));
        diyecharts.updatePieChart(this,piedata,this.data.title1[this.data.value]);
      });
  },
  UserProductGetChannel() {
    this.setData({
      channelinfoloading:true
    })

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "UserProduct/GetChannel",
        data,
        load: false,
      })
      .then((res) => {
        // 为不同渠道定义不同的颜色
        const channelColors = {
          零售: "#1677ff", // 蓝色
          煤改气: "#52c41a", // 绿色
          电商: "#faad14", // 黄色
          工程: "#ff4d4f", // 红色
          // 默认颜色，如果没有匹配到上面的渠道名称
          default: "#722ed1", // 紫色
        };

        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          const totalQauntity = Number(item.totalQauntity || 0);
          const enterQauntity = Number(item.enterQauntity || 0);

          // 避免除以零的情况
          let rate = totalQauntity > 0 ? enterQauntity / totalQauntity : 0;

          // 确保 rate 在 0-1 之间
          rate = Math.min(1, Math.max(0, rate));

          // 计算百分比并四舍五入到整数
          const ratePercent = Math.round(rate * 100);

          // 获取该渠道对应的颜色，如果没有定义则使用默认颜色
          const color = channelColors[item.title] || channelColors.default;

          // 返回处理后的数据，添加 rate 字段和 color 字段
          return {
            ...item,
            rate: rate,
            // 格式化为百分比字符串，用于显示
            rateText: ratePercent + "%",
            // 添加颜色字段
            color: color,
          };
        });

        this.setData({
          channelinfoloading:false,
          channelinfo: processedData
        });
      })
      .catch((err) => {
        console.error("获取渠道录入率数据失败:", err);
      });
  },
  UserProductGetServiceReport() {
    this.setData({
      serviceReportinfoloading:true
    })
    //level:5
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: this.data.level,
      value:this.data.vractive
    };
    request
      .post({
        url: "UserProduct/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data.map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });

        this.setData({
          serviceReportinfoloading:false,
          serviceReportinfo: processedData,
        });
      });
  },
  UserProductGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(this,"chartInstanceLine");
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;
     diyecharts.showLoading(this,"chartInstanceLine")

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: this.data.selectedRegionValue,
      };
    }
    request
      .post({
        url: "UserProduct/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from(
            {
              length: 12,
            },
            (_, i) => (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }
        diyecharts.updateLineChart(this,linedata, xAxisData, timeType,"录入率");
      });
  },
  RegionGetList() {
    request
      .get({
        url: "Region/GetList",
        load: false,
      })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          // 获取区域数据
          const regions = res.data;

          // 创建区域选择列表
          const regionLabels = regions.map((item) => item.label);
          const regionColumns = [{ values: regionLabels }];

          // 默认选择第一个区域
          const firstRegion = regions[0].label;

          this.setData({
            regioninfo: regions,
            regionColumns: regionColumns,
            selectedRegion: firstRegion,
            selectedRegionValue: regions[0].value,
            regionIndex: 0,
          });

        this.init1()
        }
      });
  },
  //活跃用户汇总
  UserAnalyticsGetSummary() {
    var data = {
      dateTime: [],
    };
    request
      .post({
        url: "UserAnalytics/GetSummary",
        data,
        load: false,
      })
      .then((res) => {
        this.setData({
          summaryinfo: res.data,
        });
      });
  },
  UserAnalyticsGetProd() {
    const ensurePieReady = diyecharts.createChartGuard(this,"chartInstancePie");
    var data = {
      dateTime: [],
    };
    diyecharts.showLoading(this,"chartInstancePie")

    request
      .post({
        url: "UserAnalytics/GetProd",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        const piedata = res.data.map((e) => ({
          value: e.enterQauntity,
          name: e.title,
        }));
        diyecharts.updatePieChart(this,piedata,this.data.title1[this.data.value]);
      });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 页面加载时的初始化逻辑
    const currentYear = new Date().getFullYear().toString();
    const yearValues = this.data.yearColumns[0].values;
    const yearIndex = yearValues.indexOf(currentYear);

    // 如果找到当前年份在数组中的位置，则更新yearIndex
    if (yearIndex !== -1) {
      this.setData({
        yearIndex: yearIndex,
      });
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先设置图表初始化函数，但不会立即执行
    this.setData(
      {
        pieec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstancePie = diyecharts.initRingPieChart(
              this,
              canvas,
              width,
              height,
              dpr,

            );
            return this.chartInstancePie;
          },
        },
        lineec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstanceLine = diyecharts.initLineChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstanceLine;
          },
        },
        userInfo: wx.getStorageSync("userInfo"),
        isnew: app.globalData.isnew,
        level: app.globalData.level,
      },
      () => {
        if(!this.data.isnew){
          var userInfo=this.data.userInfo
          if(userInfo.roleId!=7&&userInfo.roleId!=6){
          // 只有在已登录状态下才加载数据
          this.RegionGetList();
          this.init();
          }
        }
      }
    );
  },

  // 登录按钮点击事件
  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },
  onUnload() {
   diyecharts.onUnload(this)
  },

  init() {
    switch (this.data.value) {
      case 0:
        this.UserProductGetSummary();
        this.UserProductGetProd();
        this.UserProductGetChannel();
        this.UserProductGetServiceReport();
        break;
      case 1:
        this.UserAnalyticsGetSummary();
        this.UserAnalyticsGetProd();
        break;
    }
  },
  init1() {
    switch (this.data.value) {
      case 0:
        this.UserProductGetStat();
        break;
      case 1:
        // 用户活跃度分析暂时没有折线图数据，但需要清空折线图显示
        this.clearLineChart();
        break;
    }
  },

  // 清空折线图显示
  clearLineChart() {
    if (this.chartInstanceLine) {
      // 清空折线图数据，显示暂无数据
      this.chartInstanceLine.setOption({
        series: [],
        xAxis: {
          data: []
        },
        legend: {
          data: []
        },
        graphic: {
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无数据",
            fill: "#999",
            fontSize: 16,
          },
        },
      }, {
        replaceMerge: ['series', 'xAxis', 'legend', 'graphic']
      });
    }
  },

  onChange(e){
    this.setData({
      vractive: e.detail.index
    });
    this.UserProductGetServiceReport();
  },

  //顶部选择日期范围的事件
  onOptionChange(e) {
    const newValue = e.detail;
    const oldValue = this.data.value;

    this.setData({
      value: newValue,
    });

    // 先执行基础数据初始化
    this.init();

    // 如果从其他选项切换到"用户录入率分析"，需要确保折线图正确加载
    if (newValue === 0 && oldValue !== 0) {
      // 延迟执行，确保图表实例已经准备好并且页面已经重新渲染
      setTimeout(() => {
        // 确保图表实例存在
        if (this.chartInstanceLine) {
          this.init1();
        } else {
          // 如果图表实例不存在，再等待一段时间
          setTimeout(() => {
            this.init1();
          }, 200);
        }
      }, 150);
    } else {
      this.init1();
    }
  },
  startTimePopup() {
    var time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },
  endTimePopup() {
    var time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },
  onConfirm(e) {
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });
    if (this.data.tType == 1) {
      this.setData(
        {
          startTime: dateUtil.getDateObject(e.detail),
        },
        () => {}
      );
    } else {
      this.setData(
        {
          endTime: dateUtil.getDateObject(e.detail),
        },
        () => {}
      );
    }

    this.init();
  },
  onClose() {
    this.setData({
      timeshow: false,
    });
  },


  // 统计 事件

 // 显示年份选择弹出层
  showYearPopup() {
    this.setData({
      yearshow: true,
    });
  },
  // 确认选择年份
  onYearConfirm(e) {
    const { value, index } = e.detail;
    const year = parseInt(value[0]);
    this.setData({
      selectedYear: year,
      yearIndex: index[0],
      yearshow: false,
    });
    this.init1()
  },


  ontjChange(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      active: index,
      timeType: index + 1, // 设置 timeType: 1-年度，2-自然月，3-自然周
    });

    this.init1()
  },


  // 取消选择年份
  onYearCancel() {
    this.setData({
      yearshow: false,
    });
  },

   // 显示区域选择弹出层
  showRegionPopup() {
    this.setData({
      regionshow: true,
    });
  },

  // 确认选择区域
  onRegionConfirm(e) {
    const { value, index } = e.detail;
    const regionLabel = value[0];

    // 找到对应的区域对象
    const selectedRegion = this.data.regioninfo.find(
      (item) => item.label === regionLabel
    );

    if (selectedRegion) {
      this.setData({
        selectedRegion: selectedRegion.label,
        selectedRegionValue: selectedRegion.value,
        regionIndex: index[0],
        regionshow: false,
      });
      // 选择年份后重新获取统计数据
        this.init1()
    }
  },

  // 取消选择区域
  onRegionCancel() {
    this.setData({
      regionshow: false,
    });
  },

  // 关闭区域选择弹出层
  onRegionClose() {
    this.setData({
      regionshow: false,
    });
  },
});
