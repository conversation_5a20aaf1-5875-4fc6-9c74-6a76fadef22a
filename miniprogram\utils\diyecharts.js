import * as echarts from "../ec-canvas/echarts";
//环状饼图
export const initRingPieChart = (that, canvas, width, height, dpr) => {
  if (that.chartInstancePie) {
    return that.chartInstancePie; // 返回现有实例
  }
  const chart = echarts.init(canvas, null, {
    width: width,
    height: height,
    devicePixelRatio: dpr,
  });
  canvas.setChart(chart);
  // 保存饼图实例，用于后续更新
  that.chartInstancePie = chart;
  // 初始化时显示加载动画
  chart.showLoading({
    text: "数据加载中...",
    color: "#1677ff",
    textColor: "#000",
    maskColor: "rgba(255, 255, 255, 0.8)",
    zlevel: 0,
  });
  // 设置初始配置（数据通过chartData动态绑定）
  const option = {
    tooltip: {
      trigger: "item",
      formatter: ({ name, value, percent }) =>
        `${name}: ${value} (${percent}%)`,
    },
    legend: {
      left: "center",
      orient: "horizontal", // 水平排列
      bottom: 12, // 放在底部
      itemWidth: 15, // 图例标记的宽度
      itemHeight: 10, // 图例标记的高度
      textStyle: {
        fontSize: 12, // 图例文字大小
      },
    },
    color: ["#1677ff", "#52c41a", "#faad14", "#ff4d4f", "#722ed1", "#45C2E0"],
    series: [
      {
        type: "pie",
        radius: ["35%", "60%"],
        center: ["50%", "40%"], // 更多上移，为底部图例留出更多空间
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 5,
        },
        label: {
          show: true, // 显示标签
          formatter: function (params) {
            // 自定义格式化函数，实现多行显示
            return [`${params.percent}%`].join("\n");
          },
          fontSize: 10, // 调整字体大小
          color: "#333", // 标签颜色
          lineHeight: 14, // 行高
        },
        data: [],
      },
    ],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      style: {
        text: "暂无数据",
        fill: "#999",
        fontSize: 16,
      },
    },
  };
  chart.setOption(option);
  return chart;
};
//饼图
export const initPieChart = (that, canvas, width, height, dpr) => {
  if (that.chartInstancePie) {
    return that.chartInstancePie; // 返回现有实例
  }
  const chart = echarts.init(canvas, null, {
    width: width,
    height: height,
    devicePixelRatio: dpr,
  });
  canvas.setChart(chart);

  // 设置初始配置（数据通过chartData动态绑定）
  const option = {
    tooltip: {
      trigger: "item",
      formatter: ({ name, value, percent }) =>
        `${name}: ${value} (${percent}%)`,
    },
    legend: {
      left: "center",
      orient: "horizontal", // 水平排列
      bottom: 12, // 放在底部
      itemWidth: 15, // 图例标记的宽度
      itemHeight: 10, // 图例标记的高度
      textStyle: {
        fontSize: 12, // 图例文字大小
      },
    },
    color: ["#1677ff", "#52c41a", "#faad14", "#ff4d4f", "#722ed1", "#45C2E0"],
    series: [
      {
        type: "pie",
        radius: "60%",
        center: ["50%", "40%"], // 更多上移，为底部图例留出更多空间
        label: {
          show: true, // 显示标签
          formatter: function (params) {
            // 自定义格式化函数，实现多行显示
            return [`${params.percent}%`].join("\n");
          },
          fontSize: 10, // 调整字体大小
          color: "#333", // 标签颜色
          lineHeight: 14, // 行高
        },
        data: [],
      },
    ],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      style: {
        text: "暂无数据",
        fill: "#999",
        fontSize: 16,
      },
    },
  };
  chart.setOption(option);

  // 保存饼图实例，用于后续更新
  that.chartInstancePie = chart;

  // 初始化时显示加载动画
  chart.showLoading({
    text: "数据加载中...",
    color: "#1677ff",
    textColor: "#000",
    maskColor: "rgba(255, 255, 255, 0.8)",
    zlevel: 0,
  });

  return chart;
};
//折线图
export const initLineChart = (that, canvas, width, height, dpr) => {
  if (that.chartInstanceLine) {
    return that.chartInstanceLine; // 返回现有实例
  }
  const chart = echarts.init(canvas, null, {
    width: width,
    height: height,
    devicePixelRatio: dpr,
  });
  canvas.setChart(chart);

    // 公共颜色配置（支持6个系列循环）
  const colorPalette = [
    "#1677ff",   // 主蓝
    "#52c41a",   // 绿
    "#faad14",   // 橙
    "#ff4d4f",   // 红
    "#722ed1",   // 紫
    "#45C2E0"    // 浅蓝
  ];

  // 动态配置项（数据通过 this.data 获取）
  const option = {
    grid: {
      left: "10%", // 左间距
      right: "10%", // 右间距
    },
    // 当 x 轴数据数量超过 8 个时启用滑动功能，但自然月（timeType = 2）除外
    dataZoom: [],
    tooltip: {
      trigger: "axis", // 折线图必须用 axis 模式
    },
    legend: {
      top: 20,
      data: [], // 使用动态标题
    },
    color: colorPalette,
    xAxis: {
      type: "category", // 分类轴
      data: [], // X轴数据（如日期数组）
      axisLabel: {
        rotate: 0, // 不旋转标签
        interval: 0, // 强制显示所有标签
      },
    },
    yAxis: {
      type: "value", // 数值轴
    },
    series: [],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      style: {
        text: "暂无数据",
        fill: "#999",
        fontSize: 16,
      },
    },
  };

  chart.setOption(option);
  // 保存折线图实例，用于后续更新
  that.chartInstanceLine = chart;

  // 初始化时显示加载动画
  chart.showLoading({
    text: "数据加载中...",
    color: "#1677ff",
    textColor: "#000",
    maskColor: "rgba(255, 255, 255, 0.8)",
    zlevel: 0,
  });

  return chart;
};

// 横向堆叠柱状图初始化
export const initHorizontalStackedBar = (that, canvas, width, height, dpr) => {
  if (that.chartInstanceStackedBar) return that.chartInstanceStackedBar;

  const chart = echarts.init(canvas, null, {
    width: width,
    height: height,
    devicePixelRatio: dpr,
  });
  canvas.setChart(chart);

  // 颜色配置（与之前图表保持一致）
  const colorPalette = [
    "#1677ff", "#52c41a", "#faad14",
    "#ff4d4f", "#722ed1", "#45C2E0"
  ];

  // 设置柱子的固定高度和间距
  const barHeight = 30; // 固定每个柱子的高度为30px
  const categoryGap = '30%'; // 分类之间的间距

  const baseOption = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      left: "center",
      orient: "horizontal", // 水平排列
      top: 10,
      itemWidth: 15, // 图例标记的宽度
      itemHeight: 10, // 图例标记的高度
      textStyle: {
        fontSize: 12, // 图例文字大小
      },
      selectedMode: false // 禁用图例点击切换功能
    },
    grid: {
      left: "10%", // 左间距
      right: "10%", // 右间距
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
        fontSize: 10
      },
      max: 100,
      // 显示竖向网格线
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E0E0E0'
        }
      },
      // 设置刻度
      splitNumber: 5, // 将x轴分成5份，即0%, 25%, 50%, 75%, 100%
      axisTick: {
        show: true,
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'category',
      axisLine: { show: false },
      axisTick: { show: false },
      data: [],
      axisLabel: {
        margin: 15, // 增加标签与轴线的距离
        fontSize: 12,
        color: '#666'
      }
    },
    color: colorPalette,
    series: [{
      type: 'bar',
      stack: 'total',
      barWidth: barHeight, // 设置固定高度
      barGap: 0,           // 同系列的柱子之间没有间距
      barCategoryGap: categoryGap, // 不同分类的柱子之间的间距
      data: []
    }],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      style: {
        text: "暂无数据",
        fill: "#999",
        fontSize: 16
      }
    }
  };

  chart.setOption(baseOption);
  that.chartInstanceStackedBar = chart;

  chart.showLoading({
    text: "数据加载中...",
    color: "#1677ff",
    textColor: "#000",
    maskColor: "rgba(255, 255, 255, 0.8)",
    zlevel: 0
  });

  return chart;
};

//更新饼图数据
export const updatePieChart = (that, piedata, seriesname) => {
  if (!that.chartInstancePie) {
    return setTimeout(() => updatePieChart(that, piedata, seriesname), 100);
  }

  const chart = that.chartInstancePie;
  chart.hideLoading();

  // 检查是否有数据
  const hasValidData = piedata && piedata.length > 0;

  if (hasValidData) {
    // 有数据，更新图表数据
    chart.setOption(
      {
        series: [
          {
            name: seriesname,
            data: piedata,
          },
        ],
        graphic: null, // 清除无数据提示
      },
      {
        replaceMerge: ["graphic"], // 关键修改：替换而非合并
      }
    );
  } else {
    // 无数据，显示无数据提示
    chart.setOption(
      {
        series: [
          {
            data: [],
          },
        ],
        graphic: {
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无数据",
            fill: "#999",
            fontSize: 16,
          },
        },
      },
      {
        replaceMerge: ["graphic"], // 关键修改：替换而非合并
      }
    );
  }

  // 自动调整尺寸
  setTimeout(() => chart.resize(), 50);
};

//更新折线图数据
// 折线图数据更新（兼容单/多组）
export const updateLineChart = (that,rawData,xAxisData,timeType,seriesNames) => {
  if (!that.chartInstanceLine) {
    return setTimeout(() => updateLineChart(that, rawData, xAxisData, timeType, seriesNames), 100);
  }

  const chart = that.chartInstanceLine;
  chart.hideLoading();

  // 数据规范化处理
  const isMultiSeries = Array.isArray(rawData?.[0]);
  const dataSets = isMultiSeries ? rawData : [rawData];
  const names = Array.isArray(seriesNames) ?
    seriesNames :
    [seriesNames || '指标'];

  // 生成系列配置
  const seriesConfig = dataSets.map((data, index) => {
    // 获取当前系列的基础颜色
    const baseColor = chart.getOption().color[index % 6];

    // 解析颜色为RGB值
    let rgb = [22, 119, 255]; // 默认蓝色

    // 尝试从颜色字符串中提取RGB值
    if (baseColor.startsWith('#')) {
      // 处理十六进制颜色
      const hex = baseColor.substring(1);
      if (hex.length === 6) {
        rgb = [
          parseInt(hex.substring(0, 2), 16),
          parseInt(hex.substring(2, 4), 16),
          parseInt(hex.substring(4, 6), 16)
        ];
      }
    } else if (baseColor.startsWith('rgb')) {
      // 处理rgb颜色
      const matches = baseColor.match(/\d+/g);
      if (matches && matches.length >= 3) {
        rgb = [
          parseInt(matches[0]),
          parseInt(matches[1]),
          parseInt(matches[2])
        ];
      }
    }

    return {
      name: names[index] || `系列${index + 1}`,
      type: "line",
      smooth: true,
      symbol: ["circle", "rect", "triangle"][index % 3], // 不同形状区分
      symbolSize: 6,
      showSymbol: data.length < 30,  // 数据点多时隐藏符号
      itemStyle: {
        color: baseColor
      },
      lineStyle: {
        width: index === 0 ? 2 : 1.5 // 突出第一条线
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.4)` },
          { offset: 1, color: `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.05)` }
        ])
      },
      label: {
        show: data.length < 15 && index < 2, // 智能标签显示
        position: "top",
        formatter: "{c}%",
        fontSize: 10,
        color: "#333"
      },
      data: data
    };
  });

  // 数据缩放配置
  const needDataZoom = timeType !== 2 && xAxisData.length > 8;
  const chartWidth = chart.getWidth();
  const visibleCount = Math.floor(chartWidth / 80); // 按标签宽度计算
  const dataZoom = needDataZoom ? [{
    type: "slider",
    show: false,
    start: 0,
    end: Math.min(100, (visibleCount / xAxisData.length) * 100),
    xAxisIndex: 0
  }, {
    type: "inside",
    start: 0,
    end: Math.min(100, (visibleCount / xAxisData.length) * 100)
  }] : [];

  // 检查有效数据
  const hasValidData = dataSets.some(d => d?.some(v => v !== null && v !== undefined));

  // 更新配置
  chart.setOption({
    dataZoom: dataZoom,
    xAxis: {
      data: xAxisData,
      axisLabel: {
        rotate:  0,
        interval: 0,
        formatter: (value) => {
          return xAxisData.length > 6 ?
            value.substring(0, 4) + (value.length > 4 ? "..." : "") :
            value;
        }
      }
    },
    legend: {
      data: names,
      selected: names.reduce((obj, name) => {
        obj[name] = true;
        return obj;
      }, {})
    },
    series: seriesConfig,
    graphic: hasValidData ? null : {
      type: "text",
      left: "center",
      top: "middle",
      style: {
        text: "暂无数据",
        fill: "#999",
        fontSize: 16
      }
    }
  }, {
    replaceMerge: ['series', 'legend', 'xAxis', 'dataZoom','graphic']
  });

  // 自动resize
  setTimeout(() => chart.resize(), 50);
};

// 更新堆叠柱状图数据
export const updateHorizontalStackedBar = (that, rawData, categories, seriesNames) => {
  if (!that.chartInstanceStackedBar) {
    return setTimeout(() => updateHorizontalStackedBar(that, rawData, categories, seriesNames), 100);
  }

  const chart = that.chartInstanceStackedBar;
  chart.hideLoading();

  // 获取图表当前的颜色配置
  const colorPalette = chart.getOption().color;

  // 检查原始数据是否有效
  const hasRawData = rawData && Array.isArray(rawData) && rawData.length > 0 &&
                    rawData.some(series => Array.isArray(series) && series.some(v => v > 0));

  // 数据预处理：转换为百分比
  const processedData = hasRawData ? processStackData(rawData) : [];
  const hasData = hasRawData && processedData.some(series => series.data && series.data.some(v => v > 0));

  // 调试输出
  console.log('横向堆叠柱状图数据状态:', {
    hasRawData,
    rawDataLength: rawData ? rawData.length : 0,
    processedDataLength: processedData.length,
    hasData
  });

  const seriesConfig = processedData.map((series, index) => ({
    name: seriesNames[index] || `类别${index+1}`,
    type: 'bar',
    stack: 'total',
    emphasis: { focus: 'series' },
    label: {
      show: true,
      position: 'insideRight',
      formatter: ({ value }) => value > 5 ? `${value}%` : '',
      fontSize: 10,
      color: '#fff'
    },
    data: series.data,
    itemStyle: {
      borderRadius: [0, 4, 4, 0],
      color: colorPalette[index % colorPalette.length]
    }
  }));

  // 计算柱子高度和间距
  const barHeight = 30; // 固定每个柱子的高度为30px
  const categoryGap = '30%'; // 分类之间的间距

  chart.setOption({

    xAxis: {
      // 显示竖向网格线
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E0E0E0'
        }
      },
      // 设置刻度
      splitNumber: 5, // 将x轴分成5份，即0%, 25%, 50%, 75%, 100%
      axisTick: {
        show: true,
        alignWithLabel: true
      },
      max: 100 // 确保最大值为100%
    },
    yAxis: {
      data: categories,
      axisLabel: {
        margin: 15, // 增加标签与轴线的距离
        fontSize: 12,
        color: '#666'
      }
    },
    series: seriesConfig.map(series => ({
      ...series,
      barWidth: barHeight, // 设置固定高度
      barGap: 0,           // 同系列的柱子之间没有间距
      barCategoryGap: categoryGap // 不同分类的柱子之间的间距
    })),
    grid: {
      left: '15%',    // 增加左侧空间，确保标签显示完整
      right: '5%',
      top: '20%',
      bottom: '10%'
    },
    graphic: hasData ? null : {
      type: "text",
      left: "center",
      top: "middle",
      style: { text: "暂无数据", fill: "#999", fontSize: 16 }
    }
  }, { replaceMerge: ['series', 'yAxis', 'xAxis', 'grid', 'graphic',] });

  // 自动调整尺寸
  setTimeout(() => chart.resize(), 50);
};

// 数据预处理方法
function processStackData(rawData) {
  // 输入数据格式：[[系列1数据], [系列2数据], ...]
  // 例如：[[100, 200], [300, 400]] 表示两个系列，每个系列两个柱子

  // 检查数据有效性
  if (!rawData || !Array.isArray(rawData) || rawData.length === 0 ||
      !rawData[0] || !Array.isArray(rawData[0]) || rawData[0].length === 0) {
    console.warn('processStackData: 无效的数据格式', rawData);
    return [];
  }

  try {
    // Step 1: 转置数据矩阵
    const transposed = rawData[0].map((_, colIndex) =>
      rawData.map(row => {
        // 确保每个值都是数字
        const val = row[colIndex];
        return typeof val === 'number' ? val : 0;
      })
    );

    // Step 2: 计算每个柱子的总和
    const sums = transposed.map(column =>
      column.reduce((sum, val) => sum + val, 0)
    );

    // Step 3: 转换为百分比
    return rawData.map(series => ({
      data: series.map((value, index) => {
        // 确保值是数字
        const val = typeof value === 'number' ? value : 0;
        // 确保百分比不超过100
        return sums[index] === 0 ? 0 : Math.min(100, Math.round((val / sums[index]) * 100));
      })
    }));
  } catch (error) {
    console.error('processStackData 处理数据时出错:', error);
    return [];
  }
}


//柱状图初始化
export const initBarChart = (that, canvas, width, height, dpr) => {
  console.log("初始化柱状图", { width, height, dpr });

  if (that.chartInstanceBar) {
    console.log("柱状图实例已存在，返回现有实例");
    return that.chartInstanceBar; // 返回现有实例
  }

  try {
    const chart = echarts.init(canvas, null, {
      width: width,
      height: height,
      devicePixelRatio: dpr,
    });
    canvas.setChart(chart);

    // 保存柱状图实例，用于后续更新
    that.chartInstanceBar = chart;
    console.log("柱状图实例已创建");

    // 初始化时显示加载动画
    chart.showLoading({
      text: "数据加载中...",
      color: "#1677ff",
      textColor: "#000",
      maskColor: "rgba(255, 255, 255, 0.8)",
      zlevel: 0,
    });

    // 设置初始配置
    const option = {
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" },
        formatter: function(params) {
          let result = params[0].name + '<br/>';
          params.forEach(param => {
            result += param.seriesName + ': ' + param.value + '%<br/>';
          });
          return result;
        }
      },
      legend: {
        data: ["今年", "去年"],
        left: "center",
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: ["完工率", "满意度", "录入率", "投诉完成率", "满意评价率", "接单及时率"],
        axisLabel: {
          interval: 0,
          rotate: 30, // 改为0度，即水平显示
          fontSize: 10
        }
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: '{value}%'
        },
        max: 100
      },
      color: ["#1677ff", "#faad14"],
      series: [
        {
          name: "今年",
          data: [0, 0, 0, 0, 0, 0],
          type: "bar",
          barWidth: 15,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            fontSize: 10
          }
        },
        {
          name: "去年",
          data: [0, 0, 0, 0, 0, 0],
          type: "bar",
          barWidth: 15,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            fontSize: 10
          }
        }
      ]
      // 初始化时不显示"暂无数据"的文字提示
    };

    chart.setOption(option);
    console.log("柱状图初始配置已设置");

    // 确保图表正确渲染
    setTimeout(() => {
      chart.resize();
      console.log("柱状图初始化完成并调整尺寸");
    }, 100);

    return chart;
  } catch (error) {
    console.error("初始化柱状图失败:", error);
    return null;
  }
};

// 更新柱状图数据
export const updateBarChart = (that, categories, rateData, pddRateData) => {
  if (!that.chartInstanceBar) {
    console.log("图表实例不存在，等待创建...");
    return setTimeout(() => updateBarChart(that, categories, rateData, pddRateData), 100);
  }

  const chart = that.chartInstanceBar;
  chart.hideLoading();

  // 检查是否有数据
  const hasValidData = (rateData && rateData.length > 0) || (pddRateData && pddRateData.length > 0);
  console.log("更新柱状图数据:", { categories, rateData, pddRateData, hasValidData });

  if (hasValidData) {
    // 有数据，更新图表数据
    const option = {
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" },
        formatter: function(params) {
          let result = params[0].name + '<br/>';
          params.forEach(param => {
            result += param.seriesName + ': ' + param.value + '%<br/>';
          });
          return result;
        }
      },
      legend: {
        data: ["今年", "去年"],
        left: "center",
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 0, // 改为0度，即水平显示
          fontSize: 10
        }
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: '{value}%'
        },
        max: 100
      },
      color: ["#1677ff", "#faad14"],
      series: [
        {
          name: "今年",
          data: rateData,
          type: "bar",
          barWidth: 15,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            fontSize: 10
          }
        },
        {
          name: "去年",
          data: pddRateData,
          type: "bar",
          barWidth: 15,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%',
            fontSize: 10
          }
        }
      ]
      // 不设置graphic属性，完全清除之前的"暂无数据"提示
    };

    // 先清除graphic配置
    chart.setOption({
      graphic: null
    }, {
      replaceMerge: ['graphic']
    });

    // 然后设置新的配置
    chart.setOption(option);
    console.log("柱状图数据已更新");
  } else {
    // 无数据，显示无数据提示
    chart.setOption({
      series: [
        {
          data: []
        },
        {
          data: []
        }
      ],
      graphic: {
        type: "text",
        left: "center",
        top: "middle",
        style: {
          text: "暂无数据",
          fill: "#999",
          fontSize: 16,
        },
      },
    });
    console.log("柱状图无数据");
  }

  // 自动调整尺寸
  setTimeout(() => {
    chart.resize();
    console.log("柱状图已调整尺寸");
  }, 100);
};

//卸载
export const onUnload = (that) => {
  if (that.chartInstancePie) {
    that.chartInstancePie.dispose();
    that.chartInstancePie = null;
  }
  if (that.chartInstanceLine) {
    that.chartInstanceLine.dispose();
    that.chartInstanceLine = null;
  }
  if (that.chartInstanceStackedBar) {
    that.chartInstanceStackedBar.dispose();
    that.chartInstanceStackedBar = null;
  }
  if (that.chartInstanceBar) {
    that.chartInstanceBar.dispose();
    that.chartInstanceBar = null;
  }
};
//公共图表检查方法
export const createChartGuard = (that, chartName) => {
  return () => {
    return new Promise((resolve) => {
      const check = () => {
        if (that[chartName]) {
          resolve();
        } else {
          setTimeout(check, 50);
        }
      };
      check();
    });
  };
};
//加载动画
export const showLoading = (that, chartName) => {
  if (that[chartName]) {
    // 显示加载动画
    that[chartName].showLoading({
      text: "数据加载中...",
      color: "#1677ff",
      textColor: "#000",
      maskColor: "rgba(255, 255, 255, 0.8)",
      zlevel: 0,
    });
  }
};
